import os
from Crypto.Cipher import A<PERSON>


def padding(data: bytes) -> tuple:
    pad = len(data) % 16
    if pad != 0:
        return data[:-pad], pad
    return data, 0


def unpadding(data: bytes) -> tuple:
    return padding(data)


def encrypt(key: bytes, iv: bytes, data: bytes):
    aes = AES.new(key, AES.MODE_CBC, iv=iv)
    return aes.encrypt(data)


def decrypt(key: bytes, iv: bytes, cipher: bytes):
    aes = AES.new(key, AES.MODE_CBC, iv=iv)
    data = aes.decrypt(cipher)
    return data


def encrypt_file(key: bytes, iv: bytes, file_path: str, ext: str):
    if not os.path.exists(file_path):
        return None
    stats = os.stat(file_path)
    file_size = stats.st_size
    if file_size < 10 * 1024 * 1024:
        f = open(file_path, "rb")
        if not f.readable():
            f.close()
            return None
        data = f.read()
        f.close()

        text, count = padding(data)
        rest = data[-count:]
        encrypted = encrypt(key, iv, text)
        f = open(file_path, "wb")
        count = f.write(encrypted)
        count += f.write(rest)
        f.close()
        os.renames(file_path, file_path + ext)
        return count
    else:
        return None


def decrypt_file(key: bytes, iv: bytes, file_path: str, ext: str):
    if not os.path.exists(file_path):
        return None
    stats = os.stat(file_path)
    file_size = stats.st_size
    if file_size < 10 * 1024 * 1024:
        f = open(file_path, "rb")
        if not f.readable():
            f.close()
            return None
        data = f.read()
        f.close()

        cipher, count = unpadding(data)
        rest = data[-count:]
        decrypted = decrypt(key, iv, cipher)
        f = open(file_path, "wb")
        count = f.write(decrypted)
        count += f.write(rest)
        f.close()
        os.renames(file_path, file_path.strip(ext))
        return count
    else:
        return None


if __name__ == '__main__':
    key_ = b'1234567890111213'
    iv_ = b'1312110987654321'
    file = r'D:\RansomSimulator\test\data\Majnoon Ransomware Incident Response Drill Design - 2025 R1.docx'
    ret = encrypt_file(key_, iv_, file, '.set')
    print(ret)
    ret = decrypt_file(key_, iv_, file + ".set", '.set')
    print(ret)