import platform
import win32api
import win32con


def get_current_desktop():
    if platform.system() == "Windows":
        key = win32api.RegOpenKey(win32con.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders", 0, win32con.KEY_READ)
        return win32api.RegQueryValueEx(key, "Desktop")[0]
    elif platform.system() == "Linux":
        return "~/Desktop"
    return ''
