import platform
from Util import exception

def change_current_wallpaper(image_path: str):
    if platform.system() == "Windows":
        import ctypes
        import os

        print(f"[客户端] 尝试更改壁纸: {image_path}")

        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"[客户端] 壁纸文件不存在: {image_path}")
            raise FileNotFoundError(f"壁纸文件不存在: {image_path}")

        # 调用Windows API更改壁纸
        result = ctypes.windll.user32.SystemParametersInfoW(0x0014, 0, image_path, 3)

        if result:
            print(f"[客户端] 壁纸更改成功: {image_path}")
        else:
            # 获取Windows错误码
            error_code = ctypes.windll.kernel32.GetLastError()
            print(f"[客户端] 壁纸更改失败，错误码: {error_code}")
            raise RuntimeError(f"Windows API调用失败，错误码: {error_code}")
    else:
        raise exception.UnSupportedSystemException(platform.system())


def get_current_wallpaper() -> str:
    if platform.system() == "Windows":
        import ctypes
        # 增加缓冲区大小以支持长路径，Windows最大路径长度为32767字符
        path = ctypes.create_unicode_buffer(32768)
        # 修正第二个参数，应该是缓冲区大小
        status = ctypes.windll.user32.SystemParametersInfoW(0x0073, 32768, path, 0)
        if status:
            wallpaper_path = str(path.value)
            print(f"[客户端] 获取当前壁纸路径: {wallpaper_path}")
            return wallpaper_path
        else:
            print("[客户端] 获取当前壁纸路径失败")
            return ''
    else:
        raise exception.UnSupportedSystemException(platform.system())
